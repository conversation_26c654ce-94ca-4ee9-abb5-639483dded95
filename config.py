"""
配置管理模块

统一管理HTTP请求配置、业务参数和认证信息
"""
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class OrderConfig:
    """订单业务配置类
    
    统一管理订单创建过程中的业务参数
    """
    game_id: str = "10302"
    product_type: int = 1
    product_quantity: int = 1
    easy_buy: bool = False
    pay_mode: int = 1
    service_mode: int = 1
    biz_channel: int = 1
    biz_line: int = 1
    trade_mode: int = 1
    purchase_limit: int = 1


class HttpConfig:
    """HTTP请求配置管理类
    
    统一管理HTTP请求的headers、cookies和URL
    """
    
    # 基础URL配置
    BASE_HOST = "www.pxb7.com"
    BASE_URL = f"https://{BASE_HOST}"
    
    # API端点配置
    ENDPOINTS = {
        "product_detail": "/api/product/web/product/detail",
        "indemnity_query": "/api/order/web/indemnity/queryInfo", 
        "prepare_order": "/api/order/web/order/prepareOrder",
        "submit_order": "/api/order/web/order/submitOrder"
    }
    
    def __init__(self, j_token: str, j_cookie: str = ""):
        """初始化HTTP配置
        
        Args:
            j_token: 认证token
            j_cookie: Cookie字符串
        """
        self.j_token = j_token
        self.j_cookie = j_cookie
        self._cookies_dict = self._parse_cookies(j_cookie)
    
    def _parse_cookies(self, cookie_string: str) -> Dict[str, str]:
        """解析cookie字符串为字典
        
        Args:
            cookie_string: cookie字符串，格式: "key1=value1; key2=value2"
            
        Returns:
            解析后的cookie字典
        """
        if not cookie_string:
            return {}
        
        cookies = {}
        for cookie in cookie_string.split('; '):
            if cookie and '=' in cookie:
                key, value = cookie.split('=', 1)
                cookies[key] = value
        return cookies
    
    def get_base_headers(self, product_id: Optional[str] = None) -> Dict[str, str]:
        """获取基础HTTP headers
        
        Args:
            product_id: 商品ID，用于设置Referer
            
        Returns:
            标准HTTP headers字典
        """
        headers = {
            "Host": self.BASE_HOST,
            "Connection": "keep-alive",
            "px-authorization-merchant": self.j_token,
            "px-authorization-user": self.j_token,
            "sec-ch-ua-platform": '"Windows"',
            "sec-ch-ua": '"Chromium";v="134", "Not:A-Brand";v="24", "Microsoft Edge";v="134"',
            "sec-ch-ua-mobile": "?0",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "client_type": "0",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty"
        }
        
        if product_id:
            headers["Referer"] = f"{self.BASE_URL}/confirm/{product_id}"
            
        return headers
    
    def get_get_headers(self, product_id: str) -> Dict[str, str]:
        """获取GET请求的headers
        
        Args:
            product_id: 商品ID
            
        Returns:
            GET请求专用headers
        """
        headers = self.get_base_headers(product_id)
        headers["Accept"] = "*/*"
        return headers
    
    def get_post_headers(self, product_id: str, content_length: int) -> Dict[str, str]:
        """获取POST请求的headers
        
        Args:
            product_id: 商品ID
            content_length: 请求体长度
            
        Returns:
            POST请求专用headers
        """
        headers = self.get_base_headers(product_id)
        headers.update({
            "Content-Length": str(content_length),
            "accept": "application/json",
            "content-type": "application/json",
            "Origin": self.BASE_URL
        })
        return headers
    
    @property
    def cookies(self) -> Dict[str, str]:
        """获取解析后的cookies字典"""
        return self._cookies_dict
    
    def get_url(self, endpoint: str, **params) -> str:
        """构建完整URL
        
        Args:
            endpoint: API端点名称
            **params: URL参数
            
        Returns:
            完整的API URL
        """
        if endpoint not in self.ENDPOINTS:
            raise ValueError(f"未知的API端点: {endpoint}")
            
        url = self.BASE_URL + self.ENDPOINTS[endpoint]
        
        if params:
            query_string = "&".join([f"{k}={v}" for k, v in params.items()])
            url += f"?{query_string}"
            
        return url