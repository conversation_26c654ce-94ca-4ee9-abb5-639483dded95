"""
螃蟹支付订单创建系统 - API接口模块

提供统一的API接口，封装完整的订单创建流程
所有认证信息通过参数传递，不依赖全局配置
"""
import asyncio
import json
import time
from typing import Optional, Dict, Any
from order_service import OrderServiceFactory
from exceptions import AuthenticationError, OrderServiceError
from logger import get_logger

# 创建API模块日志记录器
api_logger = get_logger(f"{__name__}.API")

async def alipay_process(alipay_url: str,pay_password: str, driver: Optional[Any] = None) -> None:
    """支付宝支付处理
    
    Args:
        alipay_url: 支付宝支付地址
        driver: 浏览器驱动实例（可选，默认为None）
        
    Returns:
        无返回值
    """
    if pay_password == "":
        raise ValueError("支付密码不能为空，请提供pay_password参数")

    if driver:
        # 导航到支付宝支付页面
        driver.get(alipay_url)
        
        try:
            # 等待并查找确认付款按钮
            # 使用 DrissionPage 的等待和选择器语法
            confirm_btn = driver.ele('css:div.cashierPreConfirm__action button', timeout=10)
            if confirm_btn:
                # 等待按钮可点击并点击
                confirm_btn.wait.displayed()
                confirm_btn.click()
                
                # 等待密码输入框出现
                # 查找密码输入框
                pwd_input = driver.ele('css:.my-passcode-input-native-input', timeout=10)
                if pwd_input:
                    # 点击输入框获得焦点
                    pwd_input.click()
                    # 输入支付密码
                    pwd_input.input('123456')
                    
                    # 等待支付完成 - 可以等待页面变化或特定元素出现
                    # 例如等待支付成功页面或返回商户页面
                    driver.wait.url_change(timeout=30)  # 等待URL变化
                    
                else:
                    print("未找到密码输入框")
            else:
                print("未找到确认付款按钮")
                
        except Exception as e:
            print(f"支付过程中出现错误: {str(e)}")


async def create_order_api(
    product_id: str,
    commodity_price: str,
    j_token: str,
    j_cookie: str = "",
    sell_id: Optional[str] = None,
    config_id: Optional[str] = None,
    proxy: Optional[str] = None,
    driver: Optional[Any] = None,
    pay_password: str = ""
) -> Dict[str, Any]:
    """统一的订单创建API接口
    
    这是一个统一的接口，可以执行完整的订单创建流程，包括：
    1. 获取商品销售者信息
    2. 查询保障配置
    3. 准备订单
    4. 提交订单
    
    Args:
        product_id: 商品ID
        commodity_price: 商品价格
        j_token: 认证token（必需）
        j_cookie: Cookie字符串（可选，默认为空）
        sell_id: 销售者ID（可选，自动获取）
        config_id: 保障配置ID（可选，自动获取）
        proxy: 代理服务器地址（可选）
        driver: 浏览器驱动（可选）
        """
    # 记录开始时间
    start_time = time.time()
    start_time_iso = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))
    
    if not j_token:
        end_time = time.time()
        end_time_iso = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))
        return {
            "product_id": product_id,
            "steps": {},
            "success": False,
            "error": "认证token不能为空，请提供j_token参数",
            "processing_time": round(end_time - start_time, 4),
            "start_time": start_time_iso,
            "end_time": end_time_iso
        }
    
        # 创建订单服务实例
    get_logger(f"{__name__}.create_order_api").info(f"创建订单服务实例 - 商品ID: {product_id}")
    order_service = OrderServiceFactory.create_service(j_token, j_cookie, proxy)

    api_logger.info(f"开始创建订单 - 商品ID: {product_id}")
    api_logger.info(f"[{start_time_iso}] 开始创建订单 - 商品ID: {product_id}, 价格: {commodity_price}")
        
    # 执行完整的订单创建流程
    result = await order_service.create_order_complete(
            product_id=product_id,
            commodity_price=commodity_price,
            sell_id=sell_id,
            config_id=config_id
        )

    alipay_url = result["alipay_url"]
    if alipay_url:
            api_logger.info("成功生成支付宝支付URL")
            api_logger.info(f"生成支付宝URL: {alipay_url}")
            # await alipay_process(alipay_url,pay_password, driver)
            
    # 记录结束时间
    end_time = time.time()
    end_time_iso = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))
    processing_time = round(end_time - start_time, 4)
        
    api_logger.info(f"订单创建流程完成，耗时: {processing_time}秒，状态: {'成功' if result['success'] else '失败'}")
    api_logger.info(f"[{end_time_iso}] 订单创建流程完成，耗时: {processing_time}秒")
    if result["success"]:
            api_logger.info("✅ 订单创建成功")
    else:
            api_logger.info(f"❌ 订单创建失败: {result['error']}")
        
        # 添加时间信息到结果中
    result["processing_time"] = processing_time
    result["start_time"] = start_time_iso
    result["end_time"] = end_time_iso
        
    return result
        

async def get_sellid(product_id: str, j_token: str, j_cookie: str = "", proxy: Optional[str] = None) -> str:
    """获取商品销售者信息
    
    Args:
        product_id: 商品ID
        j_token: 认证token（必需）
        j_cookie: Cookie字符串（可选，默认为空）
        proxy: 代理服务器地址（可选）
        
    Returns:
        API响应文本
        
    Raises:
        AuthenticationError: 认证token为空
        OrderServiceError: 业务处理错误
    """
    if not j_token:
        raise AuthenticationError("认证token不能为空，请提供j_token参数")
    
    order_service = OrderServiceFactory.create_service(j_token, j_cookie, proxy)
    return await order_service.get_sellid(product_id)


async def select_indemnity(product_id: str, commodity_price: str, j_token: str, j_cookie: str = "") -> str:
    """查询保障信息
    
    Args:
        product_id: 商品ID
        commodity_price: 商品价格
        j_token: 认证token（必需）
        j_cookie: Cookie字符串（可选，默认为空）
        
    Returns:
        API响应文本
        
    Raises:
        AuthenticationError: 认证token为空
        OrderServiceError: 业务处理错误
    """
    if not j_token:
        raise AuthenticationError("认证token不能为空，请提供j_token参数")
    
    order_service = OrderServiceFactory.create_service(j_token, j_cookie)
    return await order_service.select_indemnity(product_id, commodity_price)


async def prepare_order(product_id: str, sell_id: str, config_id: str, j_token: str, j_cookie: str = "", proxy: Optional[str] = None) -> str:
    """准备订单
    
    Args:
        product_id: 商品ID
        sell_id: 销售者ID
        config_id: 保障配置ID
        j_token: 认证token（必需）
        j_cookie: Cookie字符串（可选，默认为空）
        proxy: 代理服务器地址（可选）
        
    Returns:
        API响应文本
        
    Raises:
        AuthenticationError: 认证token为空
        OrderServiceError: 业务处理错误
    """
    if not j_token:
        raise AuthenticationError("认证token不能为空，请提供j_token参数")
    
    order_service = OrderServiceFactory.create_service(j_token, j_cookie, proxy)
    return await order_service.prepare_order(product_id, sell_id, config_id)


async def submit_order(product_id: str, sell_id: str, config_id: str, j_token: str, j_cookie: str = "", proxy: Optional[str] = None) -> str:
    """提交订单
    
    Args:
        product_id: 商品ID
        sell_id: 销售者ID
        config_id: 保障配置ID
        j_token: 认证token（必需）
        j_cookie: Cookie字符串（可选，默认为空）
        proxy: 代理服务器地址（可选）
        
    Returns:
        API响应文本
        
    Raises:
        AuthenticationError: 认证token为空
        OrderServiceError: 业务处理错误
    """
    if not j_token:
        raise AuthenticationError("认证token不能为空，请提供j_token参数")
    
    order_service = OrderServiceFactory.create_service(j_token, j_cookie, proxy)
    return await order_service.submit_order(product_id, sell_id, config_id)