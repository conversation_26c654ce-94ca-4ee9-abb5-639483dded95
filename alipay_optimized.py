"""
优化后的支付宝转换模块

主要优化：
1. 预初始化加密对象，避免重复创建
2. 异步网络请求
3. 连接复用
4. 减少重复计算
"""
import asyncio
import aiohttp
import json
import base64
import re
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5 as Cipher_pkcs1_v1_5
from Crypto.Cipher import DES3
from Crypto.Util.Padding import pad, unpad
from typing import Optional
from logger import get_logger

# JSON性能优化
try:
    import orjson as json_lib
    
    def json_loads(data: str):
        return json_lib.loads(data)
    
    def json_dumps(data, **kwargs):
        return json_lib.dumps(data).decode('utf-8')
        
except ImportError:
    import json as json_lib
    
    def json_loads(data: str):
        return json_lib.loads(data)
    
    def json_dumps(data, **kwargs):
        return json_lib.dumps(data, **kwargs)


class OptimizedAliPay:
    """优化后的支付宝转换类
    
    主要优化：
    - 预初始化加密对象
    - 异步网络请求
    - 连接复用
    - 减少重复计算
    """
    
    def __init__(self):
        """初始化优化后的支付宝处理器"""
        self.logger = get_logger(f"{__name__}.OptimizedAliPay")
        
        # 预定义密钥
        self.private_key = '''-----BEGIN RSA PRIVATE KEY-----
            MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCL1i+fn62CHFUAEIlsuSU8DM4PhqjqiW4itBCne/MRfKIsDCdevuw1OHCV80ICUjd5ke+Rr7AvwO3gU88M6UjGvCrJK/krElYigJswehgjmRMJAay8tO78h2ABgwvoUfkPOP7IHJaxiUaZGGzifd2cFNNRgRN9H20HTvlzmIhgBOcSVEVmJugo2BjzOzU4/Lu10D/arOn9EeofXXpfEBAo799OTm7nqXzKMA9Egdl/8ZIPhXrbMl6CVg4syYV5t46T0W7frRw7L78b2O3inxeH2LchEjDHKDQTNiexJ/xFQVCh1iVgoIwaKBLC6LMTPh06gZVDURKJB9wnfyTJ+hpNAgMBAAECggEABykkryviGrOQtrwiDWs9uOF++9SNedUnyqcl4y25uL+FHnRQ380vE1qciVE3pB7JsHQErJUulINwqvgfti2MCIFCP6L803PQ7Vtglw7phYklLGTlj5REWLIl/G3VgkQQWPM2ONEd9mFtOBHEIaUIYCHA4H+Xm+SsFJ+6rmy1LxV9ZW5ddDeGRIgZLCIDDJtsaEuc18RbvFE2S98lha1LAdL1Una210Rw70IUH358VzsRf/jfsaFeZZGnF1n7PXw5eor5aEKguCioiTqTyRfPtege2KdcRrhUWP2+JuYhFvA6eFq3/87hHizI6/Yrg2AggYUUY6gA6eS1tjE1gqXTEQKBgQDoed+flyVnqsCX3jWYybpsUy5rBDzVZODcoVbvgEl8Xw5R7nR93+R1g2NYmDPnS5XiEKWtNNYewo6Pv2pmRMbuyKX6B41a0IsDMIG1vAqbdc7Xj91eLCfTVPqS0AiGIFJX5CWFOCs70rglG3h/nZe5hcR11ltOAWqSqRlvcGcrUwKBgQCZ/I4O4T0d7s7BJAkHo41LIo3pVA7IkRn4DQTpF1BxElnWMNv6Vov0YbnXz84cfGmq4bTOX8ueHSTP0a0JR0nMvbWukj9p8C7nGmXJuHXpLJjraHTT+O8OkE30VajEyP2CnPNryfR66oi2XpUpRcXTj7KaVb2UyResIgbWUHiP3wKBgQCQUA+UtzQeHW5/GA73cMrMMfrPrgrBgWThMTqRZHa5wRxXmgowlYrxtAU42wrlWxOJCUJ/uhvtbmMnMvEu2SUQ1/fItWV3aZvR+AudMET5anFjeUg3DHwQgWEnQAL6mBflvZfZEhwsf8uWJW5w8fhcz4A8kjuNue1Za6WBeypgRwKBgB4Ml9g1ggy2TmiIVK7F7suruY+/1Ia1MiEiwUOPRiZak2dl73eBrhwJeg+wQKN0b9Zl5zeioASB4W4gl6jI3ZDzsGGZroBI245Dq3ta4L+Y8Vp27t1ypYvtAxlcIewM4NO9Nw9gwLG/1N/pwyfjssAfOZY+hxliyJjRpw3pdC13AoGAETuOBJDCHiGqBAFL7L0ctGdynOKOC4aMxbCeWYLQWYr3FtMJ11rCib4XxfGN6k/dXAcF97ivZjs8xCSHHx4ruzAFJyF9dshpMMeZB6pWDa0NX/jB9zYMGEbFiGz6uBqCYeL028xh8Nn2tmoXl5hEK5/QuUM0XSRt9ELLjFYAm4s=
            -----END RSA PRIVATE KEY-----
            '''
        
        self.public_key = '''-----BEGIN PUBLIC KEY-----
            MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDENksAVqDoz5SMCZq0bsZwE+I3NjrANyTTwUVSf1+ec1PfPB4tiocEpYJFCYju9MIbawR8ivECbUWjpffZq5QllJg+19CB7V5rYGcEnb/M7CS3lFF2sNcRFJUtXUUAqyR3/l7PmpxTwObZ4DLG258dhE2vFlVGXjnuLs+FI2hg4QIDAQAB
            -----END PUBLIC KEY-----'''
        
        self.key = '23h4fhdilenbs741kogue1tl'
        
        # 🚀 优化1: 预初始化加密对象，避免重复创建
        self._init_crypto_objects()
        
        # 🚀 优化2: 预构建固定的请求模板
        self._init_request_template()
        
        # 连接管理
        self._session: Optional[aiohttp.ClientSession] = None
        
    def _init_crypto_objects(self):
        """预初始化加密相关对象"""
        try:
            # 预创建RSA密钥对象
            self._rsa_private_key = RSA.importKey(self.private_key)
            self._rsa_public_key = RSA.importKey(self.public_key)
            
            # 预创建加密器
            self._rsa_public_cipher = Cipher_pkcs1_v1_5.new(self._rsa_public_key)
            self._rsa_private_cipher = Cipher_pkcs1_v1_5.new(self._rsa_private_key)
            
            # 预创建DES3密钥
            self._des3_key = self.key.encode()
            
            self.logger.info("加密对象预初始化完成")
        except Exception as e:
            self.logger.error(f"加密对象初始化失败: {str(e)}")
            raise
    
    def _init_request_template(self):
        """预构建请求模板"""
        self._base_json_template = {
            "tid": "qwertyuiopasdfghjklzxcvbnm",
            "user_agent": "Msp/9.1.5 (Android 12;Linux 4.4.146;zh_CN;http;540*960;21.0;WIFI;87699552;32617;1;000000000000000;000000000000000;8efce46e85;GOOGLE;H002;false;00:00:00:00:00:00;-1.0;-1.0;sdk-and-lite;65r7u2pfruicqrn;r2agza5c56pzmev;<unknown ssid>;02:00:00:00:00:00)",
            "has_alipay": False,
            "has_msp_app": False,
            "app_key": "2021002145675770",
            "utdid": "z1x2c3v4v5v6v78v9",
            "new_client_key": "8efcf8b134",
            "action": {"type": "cashier", "method": "main"},
            "gzip": True
        }
        
        self._request_headers = {
            'Accept-Charset': 'UTF-8',
            'Connection': 'Keep-Alive',
            'Content-Type': 'application/octet-stream;binary/octet-stream',
            'Cookie': 'zone=RZ43A',
            'Cookie2': '$Version=1',
            'Host': 'mcgw.alipay.com',
            'Keep-Alive': 'timeout=180, max=100',
            'User-Agent': 'msp'
        }
        
        self._api_url = 'http://mcgw.alipay.com/gateway.do'
        
        self.logger.info("请求模板预构建完成")
    
    def _encrypt_3des_optimized(self, data: str) -> bytes:
        """优化后的3DES加密"""
        cipher = DES3.new(self._des3_key, DES3.MODE_ECB)
        padded_data = pad(data.encode(), DES3.block_size)
        return cipher.encrypt(padded_data)

    def _decrypt_3des_optimized(self, data: str) -> str:
        """优化后的3DES解密"""
        ct_bytes = base64.b64decode(data)
        cipher = DES3.new(self._des3_key, DES3.MODE_ECB)
        plain_text = unpad(cipher.decrypt(ct_bytes), DES3.block_size)
        return plain_text.decode()

    def _rsa_encrypt_optimized(self, message: str) -> str:
        """优化后的RSA加密"""
        cipher_text = base64.b64encode(
            self._rsa_public_cipher.encrypt(message.encode())
        ).decode()
        return cipher_text

    def _rsa_decrypt_optimized(self, text: str) -> str:
        """优化后的RSA解密"""
        retval = self._rsa_private_cipher.decrypt(
            base64.b64decode(text), 'ERROR'
        ).decode('utf-8')
        return retval
    
    async def _ensure_session(self):
        """确保HTTP会话存在"""
        if self._session is None or self._session.closed:
            # 🚀 优化3: 创建优化的连接器
            connector = aiohttp.TCPConnector(
                limit=50,
                limit_per_host=20,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
            
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            
            self._session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers=self._request_headers
            )
    
    async def _close_session(self):
        """关闭HTTP会话"""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None
    
    async def convert_alipay_to_h5_async(self, alipay_sdk: str) -> str:
        """异步版本的支付宝转换方法 - 核心优化
        
        Args:
            alipay_sdk: 支付宝SDK字符串
            
        Returns:
            支付宝H5支付URL
        """
        try:
            # 🚀 优化4: 使用预构建的模板，减少字符串拼接
            json_request_data = self._base_json_template.copy()
            json_request_data["external_info"] = alipay_sdk
            
            json_request = json_dumps(json_request_data)
            
            # 🚀 优化5: 使用预初始化的加密对象
            encrypted_data = base64.b64encode(
                self._encrypt_3des_optimized(json_request)
            ).decode()
            
            parameter1 = self._rsa_encrypt_optimized(self.key)
            parameter2 = format(len(parameter1), '08X')
            parameter3 = format(len(encrypted_data), '08X')
            req_data = parameter2 + parameter1 + parameter3 + encrypted_data
            
            # 构建请求数据
            request_data = {
                "data": {
                    "device": "GOOGLE-H002", 
                    "namespace": "com.alipay.mobilecashier", 
                    "api_name": "com.alipay.mcpay",
                    "api_version": "4.0.2", 
                    "params": {"req_data": req_data}
                }
            }
            
            # 🚀 优化6: 异步网络请求
            await self._ensure_session()
            
            async with self._session.post(
                self._api_url, 
                json=request_data
            ) as response:
                response.raise_for_status()
                response_text = await response.text()
            
            # 解析响应
            json_data = json_loads(response_text)
            res_data = self._decrypt_3des_optimized(json_data['data']['params']['res_data'])
            json_data = json_loads(res_data)
            
            # 提取URL
            urldata = json_data['form']['onload']['name']
            match = re.search(r"https://[^\s']+", urldata)
            
            if match:
                url = match.group(0)
                self.logger.info(f"支付URL生成成功: {url}")
                return url
            else:
                self.logger.warning("未找到支付URL")
                return ''
                
        except Exception as e:
            self.logger.error(f"支付URL生成失败: {str(e)}")
            return ''
    
    # 保持向后兼容的同步方法
    def convert_alipay_to_h5(self, alipay_sdk: str) -> str:
        """同步版本的支付宝转换方法（向后兼容）"""
        return asyncio.run(self.convert_alipay_to_h5_async(alipay_sdk))
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self._close_session()
