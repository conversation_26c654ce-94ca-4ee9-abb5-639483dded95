"""
订单服务模块 - 性能优化版本

封装完整的订单创建业务流程，包含以下优化：
1. 异步并行化优化
2. HTTP连接池复用
3. 支付URL生成优化  
4. JSON解析性能优化
"""
import asyncio
import aiohttp
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager
from config import OrderConfig
from http_client import HttpClient, HttpClientManager
from exceptions import (
    OrderServiceError, 
    AuthenticationError, 
    ParameterValidationError,
    APIResponseError,
    OrderCreationError,
    PaymentError,
    NetworkError
)
from logger import get_logger

# JSON性能优化 - 优先使用orjson
try:
    import orjson as json_lib
    
    def json_loads(data: str):
        return json_lib.loads(data)
    
    def json_dumps(data, **kwargs):
        # orjson返回bytes，需要decode
        return json_lib.dumps(data).decode('utf-8')
        
except ImportError:
    import json as json_lib
    
    def json_loads(data: str):
        return json_lib.loads(data)
    
    def json_dumps(data, **kwargs):
        return json_lib.dumps(data, **kwargs)


class OptimizedOrderService:
    """优化后的订单服务类
    
    提供高性能的订单创建业务流程
    """
    
    def __init__(self, j_token: str, j_cookie: str = "", proxy: Optional[str] = None):
        """初始化订单服务
        
        Args:
            j_token: 认证token
            j_cookie: Cookie字符串
            proxy: 代理服务器地址
            
        Raises:
            AuthenticationError: 认证token为空时抛出
        """
        if not j_token:
            raise AuthenticationError("认证token不能为空")
            
        self.j_token = j_token
        self.j_cookie = j_cookie
        self.proxy = proxy
        self.order_config = OrderConfig()
        self.logger = get_logger(f"{__name__}.OptimizedOrderService")
        
        # 连接管理
        self._session: Optional[aiohttp.ClientSession] = None
        self._http_client: Optional[HttpClient] = None
        
        # 支付处理优化
        self._alipay_processor = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self._close_session()
    
    async def _ensure_session(self):
        """确保HTTP会话已创建"""
        if self._session is None or self._session.closed:
            # 创建持久化会话
            connector = aiohttp.TCPConnector(
                limit=100,  # 连接池大小
                limit_per_host=30,  # 每个主机的连接数
                keepalive_timeout=30,  # 保持连接时间
                enable_cleanup_closed=True
            )
            
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            
            self._session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    "client_type": "1",
                    "app_version": "6.1.7",
                    "px-authorization-user": self.j_token,
                    "Content-Type": "application/json; charset=UTF-8",
                    "User-Agent": "okhttp/4.11.0"
                }
            )
            
            # 创建HTTP客户端
            self._http_client = HttpClientManager.create_client_with_session(
                self._session, self.j_token, self.j_cookie, self.proxy
            )
    
    async def _close_session(self):
        """关闭HTTP会话"""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None
            self._http_client = None
    
    async def get_sellid(self, product_id: str) -> str:
        """获取商品销售者信息 - 优化版本"""
        if not product_id:
            raise ParameterValidationError("商品ID不能为空", "product_id")
            
        try:
            await self._ensure_session()
            return await self._http_client.get(
                "product_detail",
                product_id,
                productId=product_id,
                productType=6,
                request="params"
            )
        except aiohttp.ClientError as e:
            raise NetworkError(f"获取商品销售者信息失败: {str(e)}")
    
    async def select_indemnity(self, product_id: str, commodity_price: str) -> str:
        """查询保障信息 - 优化版本"""
        if not product_id:
            raise ParameterValidationError("商品ID不能为空", "product_id")
        if not commodity_price:
            raise ParameterValidationError("商品价格不能为空", "commodity_price")
            
        data = {
            "productId": product_id,
            "bizChannel": self.order_config.biz_channel,
            "bizLine": self.order_config.biz_line,
            "gameId": self.order_config.game_id,
            "tradeMode": self.order_config.trade_mode,
            "purchaseLimit": self.order_config.purchase_limit,
            "commodityPrice": commodity_price
        }
        
        self.logger.info(f"查询商品 {product_id} 的保障信息，价格: {commodity_price}")
        
        try:
            await self._ensure_session()
            return await self._http_client.post("indemnity_query", product_id, data)
        except aiohttp.ClientError as e:
            raise NetworkError(f"查询保障信息失败: {str(e)}")
    
    async def submit_order(self, product_id: str, sell_id: str, config_id: str) -> str:
        """提交订单 - 优化版本"""
        data = {
            "submitOrderReqDTOList": [{
                "productId": product_id,
                "sellerId": sell_id,
                "gameId": self.order_config.game_id,
                "productType": self.order_config.product_type,
                "productQuantity": self.order_config.product_quantity,
                "easyBuy": self.order_config.easy_buy,
                "indemnityIdList": [config_id]
            }],
            "payMode": self.order_config.pay_mode,
            "serviceMode": self.order_config.service_mode
        }
        
        await self._ensure_session()
        return await self._http_client.post("submit_order", product_id, data)
    
    async def get_alipay_sdk(self, order_id: str) -> str:
        """获取支付宝SDK响应数据 - 优化版本"""
        url = "http://client-api.pxb7.com/api/order/mobile/pay/toPay"
        data = {
            "depositId": "",
            "payType": 1,
            "assPaymentId": "",
            "bizType": 0,
            "orderId": order_id,
            "merchantId": "",
            "orderItemId": "",
            "voucherId": ""
        }
        
        await self._ensure_session()
        async with self._session.post(url, json=data) as response:
            response.raise_for_status()
            return await response.text()
    
    def _parse_sellid_response(self, response: str) -> str:
        """解析销售者ID响应数据 - 优化版本"""
        try:
            data = json_loads(response)  # 使用优化后的JSON解析
            if not data.get("success") or not data.get("data"):
                raise APIResponseError("获取销售者信息失败：响应中无有效数据")
            return data["data"]["sellerId"]
        except Exception as e:
            raise APIResponseError(f"销售者信息响应解析失败: {str(e)}", response[:200])

    def _parse_indemnity_response(self, response: str) -> str:
        """解析保障配置响应数据 - 优化版本"""
        try:
            data = json_loads(response)  # 使用优化后的JSON解析
            if (not data.get("success") or 
                not data.get("data") or 
                len(data["data"]) == 0 or
                not data["data"][0].get("childList") or
                len(data["data"][0]["childList"]) <= 2):
                raise APIResponseError("保障配置响应结构异常：无法提取配置ID")
            
            return data["data"][0]["childList"][2]["configId"]
        except Exception as e:
            raise APIResponseError(f"保障配置响应解析失败: {str(e)}", response[:200])

    def _parse_submit_response(self, response: str) -> Dict[str, Any]:
        """解析订单提交响应数据 - 优化版本"""
        try:
            data = json_loads(response)  # 使用优化后的JSON解析

            success = data.get("success", False)
            error_code = data.get("errCode")
            error_msg = data.get("errMessage", "未知错误")
            response_data = data.get("data")

            if not success:
                if error_code == "500" and "重复购买" in error_msg:
                    self.logger.warning(f"重复下单错误: {error_msg}")
                    raise OrderCreationError(f"重复下单: {error_msg}")
                else:
                    self.logger.error(f"订单提交失败 - 错误码: {error_code}, 错误信息: {error_msg}")
                    raise OrderCreationError(f"订单提交失败: {error_msg}")

            if not response_data:
                self.logger.warning("订单提交成功但响应中无数据部分")
                return {"order_id": None, "body": None}

            result = {
                "order_id": response_data.get("orderId"),
                "body": response_data.get("body")
            }

            if not result["body"]:
                self.logger.warning("订单提交成功但缺少支付body信息")

            if result["order_id"]:
                self.logger.info(f"订单解析成功 - 订单ID: {result['order_id']}")

            return result
        except Exception as e:
            raise APIResponseError(f"订单提交响应解析失败: {str(e)}", response[:200])

    async def _process_order_steps_parallel(
        self,
        product_id: str,
        commodity_price: str,
        sell_id: Optional[str],
        config_id: Optional[str]
    ) -> Dict[str, Any]:
        """并行化处理订单创建的各个步骤 - 核心优化"""
        results = {}

        # 🚀 并行化优化：同时执行独立的API调用
        tasks = []
        task_names = []

        if not sell_id:
            tasks.append(self.get_sellid(product_id))
            task_names.append("sellid")
            self.logger.info(f"添加获取销售者信息任务 - 商品ID: {product_id}")

        if not config_id:
            tasks.append(self.select_indemnity(product_id, commodity_price))
            task_names.append("indemnity")
            self.logger.info(f"添加查询保障配置任务 - 商品ID: {product_id}")

        # 并行执行所有独立任务
        if tasks:
            self.logger.info(f"开始并行执行 {len(tasks)} 个独立任务: {task_names}")
            start_time = asyncio.get_event_loop().time()

            try:
                responses = await asyncio.gather(*tasks, return_exceptions=True)

                parallel_time = asyncio.get_event_loop().time() - start_time
                self.logger.info(f"并行任务完成，耗时: {parallel_time:.4f}秒")

                # 处理并行任务的结果
                response_idx = 0
                for i, task_name in enumerate(task_names):
                    response = responses[response_idx]
                    response_idx += 1

                    if isinstance(response, Exception):
                        raise response

                    if task_name == "sellid":
                        sell_id = self._parse_sellid_response(response)
                        self.logger.info(f'并行获取销售者ID成功: {sell_id}')
                    elif task_name == "indemnity":
                        config_id = self._parse_indemnity_response(response)
                        self.logger.info(f"并行获取保障配置ID成功: {config_id}")

            except Exception as e:
                self.logger.error(f"并行任务执行失败: {str(e)}")
                raise

        # 步骤3: 提交订单（依赖前面的结果）
        self.logger.info(f"提交订单 - 商品ID: {product_id}")
        self.logger.info(f"订单参数 - product_id: {product_id}, sell_id: {sell_id}, config_id: {config_id}")

        submit_response = await self.submit_order(product_id, sell_id, config_id)
        self.logger.info("订单提交完成")

        order_info = self._parse_submit_response(submit_response)
        results.update(order_info)

        # 步骤4: 获取支付URL
        if results.get("order_id"):
            self.logger.info(f"获取订单 {results['order_id']} 的支付URL")
            try:
                # 获取支付信息
                api_response = await self.get_alipay_sdk(results["order_id"])

                # 解析支付API响应
                try:
                    pay_data = json_loads(api_response)  # 使用优化后的JSON解析
                    if pay_data.get("success") and pay_data.get("data", {}).get("body"):
                        pay_body = pay_data["data"]["body"]

                        # 使用优化后的支付URL生成
                        if not self._alipay_processor:
                            from alipay_optimized import OptimizedAliPay
                            self._alipay_processor = OptimizedAliPay()

                        results["alipay_url"] = await self._alipay_processor.convert_alipay_to_h5_async(pay_body)
                        self.logger.info("成功生成支付宝支付URL")
                    else:
                        self.logger.warning("支付API响应中缺少body字段")
                        results["alipay_url"] = None
                except Exception as e:
                    self.logger.error(f"支付API响应解析失败: {str(e)}")
                    results["alipay_url"] = None

            except Exception as e:
                self.logger.error(f"获取支付URL失败: {str(e)}")
                results["alipay_url"] = None
        else:
            self.logger.info("缺少订单ID，跳过支付URL获取")
            results["alipay_url"] = None

        if results.get("alipay_url"):
            self.logger.info(f'支付宝URL生成成功: {results["alipay_url"]}')

        return results

    async def create_order_complete_optimized(
        self,
        product_id: str,
        commodity_price: str,
        sell_id: Optional[str] = None,
        config_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """完整的订单创建流程 - 性能优化版本

        Args:
            product_id: 商品ID
            commodity_price: 商品价格
            sell_id: 销售者ID（可选，自动获取）
            config_id: 保障配置ID（可选，自动获取）

        Returns:
            包含所有步骤结果的字典

        Raises:
            OrderServiceError: 业务流程执行错误
        """
        # 初始化结果结构
        results = {
            "product_id": product_id,
            "steps": {},
            "success": False,
            "error": None,
            "alipay_url": None,
            "body": None,
            "order_id": None,
            "errMessage": None
        }

        start_time = asyncio.get_event_loop().time()

        try:
            # 执行优化后的订单创建流程
            order_results = await self._process_order_steps_parallel(
                product_id, commodity_price, sell_id, config_id
            )

            # 更新结果
            results.update(order_results)
            results["success"] = True

            # 计算处理时间
            processing_time = asyncio.get_event_loop().time() - start_time
            results["processing_time"] = round(processing_time, 4)

            # 记录结果（使用优化后的JSON序列化）
            self.logger.info(f"订单创建成功，耗时: {processing_time:.4f}秒")
            self.logger.info(f"订单创建成功: {json_dumps(results, indent=2)}")

        except OrderServiceError as e:
            processing_time = asyncio.get_event_loop().time() - start_time
            self.logger.error(f"订单创建业务异常，耗时: {processing_time:.4f}秒 - {str(e)}")
            results["error"] = str(e)
            results["errMessage"] = str(e)
            results["success"] = False
            results["processing_time"] = round(processing_time, 4)

        except Exception as e:
            processing_time = asyncio.get_event_loop().time() - start_time
            error_msg = f"订单创建系统异常: {str(e)}"
            self.logger.error(f"系统异常，耗时: {processing_time:.4f}秒 - {error_msg}")
            results["error"] = error_msg
            results["errMessage"] = error_msg
            results["success"] = False
            results["processing_time"] = round(processing_time, 4)

        return results


class OptimizedOrderServiceFactory:
    """优化后的订单服务工厂类"""

    @staticmethod
    @asynccontextmanager
    async def create_service(j_token: str, j_cookie: str = "", proxy: Optional[str] = None):
        """创建优化后的订单服务实例

        Args:
            j_token: 认证token
            j_cookie: Cookie字符串
            proxy: 代理服务器地址

        Yields:
            配置好的优化订单服务实例
        """
        service = OptimizedOrderService(j_token, j_cookie, proxy)
        async with service:
            yield service
