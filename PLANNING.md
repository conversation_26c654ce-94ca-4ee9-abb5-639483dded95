# 螃蟹支付订单创建系统

## 项目概述
这是一个基于异步HTTP请求的订单创建系统，用于自动化处理商品订单的完整流程。

## 项目目标
- 优化现有代码，消除重复参数和配置
- 提供统一的API接口执行完整订单流程
- 提高代码可维护性和可扩展性

## 技术架构
- **语言**: Python 3.7+
- **异步框架**: asyncio + aiohttp
- **主要功能**: HTTP请求封装、订单业务流程管理

## 核心业务流程
1. **获取sellid** - 根据产品ID获取销售者信息
2. **选择保障** - 查询并选择商品保障配置
3. **准备订单** - 预处理订单信息
4. **提交订单** - 最终提交完整订单

## 代码风格约束
- 使用Google风格中文docstring
- 函数单文件不超过500行
- 按职责清晰组织模块
- 优先使用相对导入
- 遵循最佳实践与软件设计原则

## 项目结构
```
creat_order/
├── main.py           # 主API接口
├── config.py         # 配置管理
├── http_client.py    # HTTP客户端
├── order_service.py  # 订单业务服务
├── docs/            # 文档目录
└── test/            # 测试目录
```