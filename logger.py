"""
日志配置模块

提供统一的日志配置和管理
"""
import logging
import sys
from typing import Optional


def setup_logger(
    name: str = "creat_order", 
    level: str = "INFO",
    format_string: Optional[str] = None,
    include_timestamp: bool = True
) -> logging.Logger:
    """设置日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        format_string: 自定义格式字符串
        include_timestamp: 是否包含时间戳
        
    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    
    # 避免重复添加handler
    if logger.handlers:
        return logger
    
    # 设置日志级别
    logger.setLevel(getattr(logging, level.upper()))
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))
    
    # 设置格式
    if format_string is None:
        if include_timestamp:
            format_string = "[%(asctime)s] %(levelname)s - %(name)s - %(message)s"
        else:
            format_string = "%(levelname)s - %(name)s - %(message)s"
    
    formatter = logging.Formatter(format_string, datefmt='%Y-%m-%d %H:%M:%S')
    console_handler.setFormatter(formatter)
    
    # 添加处理器到日志记录器
    logger.addHandler(console_handler)
    
    return logger


def get_logger(name: str = "creat_order") -> logging.Logger:
    """获取日志记录器

    Args:
        name: 日志记录器名称

    Returns:
        日志记录器实例
    """
    logger = logging.getLogger(name)

    # 如果logger没有handlers，说明还没有被配置，需要设置
    if not logger.handlers:
        return setup_logger(name)

    return logger


# 默认日志记录器
default_logger = setup_logger()