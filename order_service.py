"""
订单服务模块

封装完整的订单创建业务流程
"""
import json
from typing import Dict, Any, Optional
from config import OrderConfig
from http_client import HttpClient, HttpClientManager
from exceptions import (
    OrderServiceError, 
    AuthenticationError, 
    ParameterValidationError,
    APIResponseError,
    OrderCreationError,
    PaymentError,
    NetworkError
)
from alipay_new import AiliPay
from logger import get_logger
import aiohttp
from alipay_util import get_alipay

class OrderService:
    """订单服务类
    
    提供完整的订单创建业务流程
    """
    
    def __init__(self, j_token: str, j_cookie: str = "", proxy: Optional[str] = None):
        """初始化订单服务
        
        Args:
            j_token: 认证token
            j_cookie: Cookie字符串
            proxy: 代理服务器地址
            
        Raises:
            AuthenticationError: 认证token为空时抛出
        """
        if not j_token:
            raise AuthenticationError("认证token不能为空")
            
        self.j_token = j_token
        self.j_cookie = j_cookie
        self.proxy = proxy
        self.order_config = OrderConfig()
        self.logger = get_logger(f"{__name__}.OrderService")
        self.AliPay_class = AiliPay()
    
    async def get_sellid(self, product_id: str) -> str:
        """获取商品销售者信息
        
        Args:
            product_id: 商品ID
            
        Returns:
            API响应文本
            
        Raises:
            NetworkError: HTTP请求错误
            ParameterValidationError: 参数验证错误
        """
        if not product_id:
            raise ParameterValidationError("商品ID不能为空", "product_id")
            
        try:
            async with HttpClientManager.with_client(
                self.j_token, self.j_cookie, self.proxy
            ) as client:
                return await client.get(
                    "product_detail",
                    product_id,
                    productId=product_id,
                    productType=6,
                    request="params"
                )
        except aiohttp.ClientError as e:
            raise NetworkError(f"获取商品销售者信息失败: {str(e)}")
    
    async def select_indemnity(self, product_id: str, commodity_price: str) -> str:
        """查询保障信息
        
        Args:
            product_id: 商品ID
            commodity_price: 商品价格
            
        Returns:
            API响应文本
            
        Raises:
            NetworkError: HTTP请求错误
            ParameterValidationError: 参数验证错误
        """
        if not product_id:
            raise ParameterValidationError("商品ID不能为空", "product_id")
        if not commodity_price:
            raise ParameterValidationError("商品价格不能为空", "commodity_price")
            
        data = {
            "productId": product_id,
            "bizChannel": self.order_config.biz_channel,
            "bizLine": self.order_config.biz_line,
            "gameId": self.order_config.game_id,
            "tradeMode": self.order_config.trade_mode,
            "purchaseLimit": self.order_config.purchase_limit,
            "commodityPrice": commodity_price
        }
        
        self.logger.info("=== 保障信息查询调试信息 ===")
        self.logger.info(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        self.logger.info(f"Cookie: {self.j_cookie}")
        self.logger.info(f"Token: {self.j_token}")
        self.logger.info("=====================================")
        
        self.logger.info(f"查询商品 {product_id} 的保障信息，价格: {commodity_price}")
        
        try:
            async with HttpClientManager.with_client(
                self.j_token, self.j_cookie, self.proxy
            ) as client:
                return await client.post("indemnity_query", product_id, data)
        except aiohttp.ClientError as e:
            raise NetworkError(f"查询保障信息失败: {str(e)}")
    
    async def prepare_order(self, product_id: str, sell_id: str, config_id: str) -> str:
        """准备订单
        
        Args:
            product_id: 商品ID
            sell_id: 销售者ID
            config_id: 保障配置ID
            
        Returns:
            API响应文本
            
        Raises:
            aiohttp.ClientError: HTTP请求错误
        """
        data = {
            "submitOrderReqDTOList": [{
                "productId": product_id,
                "sellerId": sell_id,
                "gameId": self.order_config.game_id,
                "productType": self.order_config.product_type,
                "productQuantity": self.order_config.product_quantity,
                "easyBuy": self.order_config.easy_buy,
                "indemnityIdList": [config_id]
            }],
            "payMode": self.order_config.pay_mode,
            "serviceMode": self.order_config.service_mode
        }
        
        async with HttpClientManager.with_client(
            self.j_token, self.j_cookie, self.proxy
        ) as client:
            return await client.post("prepare_order", product_id, data)
    
    async def submit_order(self, product_id: str, sell_id: str, config_id: str) -> str:
        """提交订单
        
        Args:
            product_id: 商品ID
            sell_id: 销售者ID
            config_id: 保障配置ID
            
        Returns:
            API响应文本
            
        Raises:
            aiohttp.ClientError: HTTP请求错误
        """
        data = {
            "submitOrderReqDTOList": [{
                "productId": product_id,
                "sellerId": sell_id,
                "gameId": self.order_config.game_id,
                "productType": self.order_config.product_type,
                "productQuantity": self.order_config.product_quantity,
                "easyBuy": self.order_config.easy_buy,
                "indemnityIdList": [config_id]
            }],
            "payMode": self.order_config.pay_mode,
            "serviceMode": self.order_config.service_mode
        }
        
        async with HttpClientManager.with_client(
            self.j_token, self.j_cookie, self.proxy
        ) as client:
            return await client.post("submit_order", product_id, data)
    
    async def get_alipay_sdk(self, order_id: str) -> str:
        """获取支付宝SDK响应数据
        
        Args:
            order_id: 订单ID
            
        Returns:
            API响应文本
            
        Raises:
            aiohttp.ClientError: HTTP请求错误
        """
        url = "http://client-api.pxb7.com/api/order/mobile/pay/toPay"
        data = {
            "depositId": "",
            "payType": 1,
            "assPaymentId": "",
            "bizType": 0,
            "orderId": order_id,
            "merchantId": "",
            "orderItemId": "",
            "voucherId": ""
        }
        headers = {
            "client_type": "1",
            "app_version": "6.1.7",
            "px-authorization-user": self.j_token,
            "Content-Type": "application/json; charset=UTF-8",
            "Content-Length": str(len(json.dumps(data))),
            "Host": "client-api.pxb7.com",
            "Connection": "Keep-Alive",
            "User-Agent": "okhttp/4.11.0"
        }

        async with HttpClientManager.with_client(
            self.j_token, self.j_cookie, self.proxy
        ) as client:
            # 直接使用aiohttp session，因为这个API使用不同的主机
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=data, headers=headers) as response:
                    response.raise_for_status()
                    return await response.text()

    def _parse_sellid_response(self, response: str) -> str:
        """解析销售者ID响应数据
        
        Args:
            response: API响应文本
            
        Returns:
            销售者ID
            
        Raises:
            APIResponseError: 响应解析失败
        """
        try:
            data = json.loads(response)
            if not data.get("success") or not data.get("data"):
                raise APIResponseError("获取销售者信息失败：响应中无有效数据")
            return data["data"]["sellerId"]
        except (json.JSONDecodeError, KeyError) as e:
            raise APIResponseError(f"销售者信息响应解析失败: {str(e)}", response[:200])

    def _parse_indemnity_response(self, response: str) -> str:
        """解析保障配置响应数据
        
        Args:
            response: API响应文本
            
        Returns:
            保障配置ID
            
        Raises:
            APIResponseError: 响应解析失败
        """
        try:
            data = json.loads(response)
            if (not data.get("success") or 
                not data.get("data") or 
                len(data["data"]) == 0 or
                not data["data"][0].get("childList") or
                len(data["data"][0]["childList"]) <= 2):
                raise APIResponseError("保障配置响应结构异常：无法提取配置ID")
            
            return data["data"][0]["childList"][2]["configId"]
        except (json.JSONDecodeError, KeyError, IndexError) as e:
            raise APIResponseError(f"保障配置响应解析失败: {str(e)}", response[:200])

    def _parse_submit_response(self, response: str) -> Dict[str, Any]:
        """解析订单提交响应数据

        Args:
            response: API响应文本

        Returns:
            包含订单信息的字典

        Raises:
            APIResponseError: 响应解析失败
            OrderCreationError: 订单创建失败
        """
        try:
            data = json.loads(response)

            # 记录响应的关键信息
            success = data.get("success", False)
            error_code = data.get("errCode")
            error_msg = data.get("errMessage", "未知错误")
            response_data = data.get("data")

            self.logger.debug(f"API响应解析 - success: {success}, errCode: {error_code}")

            # 检查业务是否成功
            if not success:
                # 根据错误码和错误信息提供更友好的提示
                if error_code == "500" and "重复购买" in error_msg:
                    self.logger.warning(f"重复下单错误: {error_msg}")
                    raise OrderCreationError(f"重复下单: {error_msg}")
                else:
                    self.logger.error(f"订单提交失败 - 错误码: {error_code}, 错误信息: {error_msg}")
                    raise OrderCreationError(f"订单提交失败: {error_msg}")

            # 检查数据部分
            if not response_data:
                self.logger.warning("订单提交成功但响应中无数据部分")
                return {"order_id": None, "body": None}

            # 安全地提取订单信息
            result = {
                "order_id": response_data.get("orderId"),
                "body": response_data.get("body")
            }

            # 验证关键字段并记录
            if not result["order_id"]:
                self.logger.warning("订单提交成功但缺少订单ID")
            if not result["body"]:
                self.logger.warning("订单提交成功但缺少支付body信息")

            if result["order_id"]:
                self.logger.info(f"订单解析成功 - 订单ID: {result['order_id']}")

            return result
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {str(e)}")
            raise APIResponseError(f"订单提交响应解析失败: {str(e)}", response[:200])
        except KeyError as e:
            self.logger.error(f"响应字段缺失: {str(e)}")
            raise APIResponseError(f"响应格式异常，缺少字段: {str(e)}", response[:200])

    async def _process_order_steps(
        self, 
        product_id: str, 
        commodity_price: str, 
        sell_id: Optional[str], 
        config_id: Optional[str]
    ) -> Dict[str, Any]:
        """处理订单创建的各个步骤
        
        Args:
            product_id: 商品ID
            commodity_price: 商品价格
            sell_id: 销售者ID（可选）
            config_id: 保障配置ID（可选）
            
        Returns:
            包含步骤结果的字典
        """
        results = {}
        
        # 步骤1: 获取sellid
        if not sell_id:
            self.logger.info(f"获取商品 {product_id} 的销售者信息")
                
            sellid_response = await self.get_sellid(product_id)
            sell_id = self._parse_sellid_response(sellid_response)
            
            self.logger.info(f'成功获取销售者ID: {sell_id}')
        
        # 步骤2: 获取保障配置
        if not config_id:
            self.logger.info(f"查询商品 {product_id} 的保障配置")
                
            indemnity_response = await self.select_indemnity(product_id, commodity_price)
            config_id = self._parse_indemnity_response(indemnity_response)
            
            self.logger.info(f"成功获取保障配置ID: {config_id}")
        
        # 步骤3: 提交订单
        self.logger.info(f"提交订单 - 商品ID: {product_id}")
        self.logger.info(f"订单参数 - product_id: {product_id}, sell_id: {sell_id}, config_id: {config_id}")
            
        submit_response = await self.submit_order(product_id, sell_id, config_id)
        
        self.logger.info("订单提交完成")
        self.logger.info(f'订单提交响应: {submit_response}')
        
        order_info = self._parse_submit_response(submit_response)
        results.update(order_info)
        
        # 步骤4: 获取支付URL
        if results.get("order_id"):
            self.logger.info(f"获取订单 {results['order_id']} 的支付URL")
            try:
                # 通过API获取支付信息
                api_response = await self.get_alipay_sdk(results["order_id"])
                self.logger.debug(f"支付API响应: {api_response}")

                # 解析支付API响应
                try:
                    pay_data = json.loads(api_response)
                    if pay_data.get("success") and pay_data.get("data", {}).get("body"):
                        pay_body = pay_data["data"]["body"]
                        # results["alipay_url"] = get_alipay(pay_body)
                        results["alipay_url"] = self.AliPay_class.convert_alipay_to_h5(pay_body)

                        self.logger.info("成功生成支付宝支付URL")
                    else:
                        self.logger.warning("支付API响应中缺少body字段")
                        results["alipay_url"] = None
                except json.JSONDecodeError as e:
                    self.logger.error(f"支付API响应解析失败: {str(e)}")
                    results["alipay_url"] = None

            except Exception as e:
                self.logger.error(f"获取支付URL失败: {str(e)}")
                results["alipay_url"] = None
        else:
            self.logger.info("缺少订单ID，跳过支付URL获取")
            results["alipay_url"] = None
        
        self.logger.info("成功生成支付宝支付URL")
        self.logger.info(f'支付宝URL: {results["alipay_url"]}')
        
        return results




    async def create_order_complete(
        self, 
        product_id: str, 
        commodity_price: str,
        sell_id: Optional[str] = None,
        config_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """完整的订单创建流程
        
        Args:
            product_id: 商品ID
            commodity_price: 商品价格
            sell_id: 销售者ID（可选，自动获取）
            config_id: 保障配置ID（可选，自动获取）
            
        Returns:
            包含所有步骤结果的字典
            
        Raises:
            OrderServiceError: 业务流程执行错误
        """
        # 初始化结果结构
        results = {
            "product_id": product_id,
            "steps": {},
            "success": False,
            "error": None,
            "alipay_url": None,
            "body": None,
            "order_id": None,
            "errMessage": None
        }
        
        try:
            # 执行订单创建流程
            order_results = await self._process_order_steps(
                product_id, commodity_price, sell_id, config_id
            )

            # 更新结果
            results.update(order_results)
            results["success"] = True

            # 记录结果
            get_logger(f"{__name__}.OrderService").info(f"订单创建成功: {json.dumps(results, ensure_ascii=False, indent=2)}")

        except OrderServiceError as e:
            self.logger.error(f"订单创建业务异常: {str(e)}")
            results["error"] = str(e)
            results["errMessage"] = str(e)
            results["success"] = False

        except Exception as e:
            error_msg = f"订单创建系统异常: {str(e)}"
            self.logger.error(error_msg)
            results["error"] = error_msg
            results["errMessage"] = error_msg
            results["success"] = False

        return results


class OrderServiceFactory:
    """订单服务工厂类
    
    提供便捷的订单服务创建方法
    """
    
    @staticmethod
    def create_service(j_token: str, j_cookie: str = "", proxy: Optional[str] = None) -> OrderService:
        """创建订单服务实例
        
        Args:
            j_token: 认证token
            j_cookie: Cookie字符串
            proxy: 代理服务器地址
            
        Returns:
            配置好的订单服务实例
        """
        return OrderService(j_token, j_cookie, proxy)