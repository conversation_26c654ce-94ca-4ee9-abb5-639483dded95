# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于异步HTTP请求的订单创建系统，用于自动化处理商品订单的完整流程。系统采用模块化架构，支持完整的订单生命周期管理。

## 核心架构

### 分层架构设计
- **API层** (`api.py`): 提供统一的API接口，包含参数化认证和时间统计功能
- **业务服务层** (`order_service.py`): 封装订单业务逻辑，管理订单创建的4个核心步骤
- **HTTP客户端层** (`http_client.py`): 统一HTTP请求管理，支持session复用和代理
- **配置层** (`config.py`): 分离业务配置（OrderConfig）和HTTP配置（HttpConfig）
- **支付工具层** (`alipay.py`): 支付宝签名工具，调用Windows DLL库
- **入口层** (`main.py`): 轻量级示例和程序入口

### 核心业务流程
订单创建遵循固定的4步流程：
1. **获取sellid** - 根据product_id获取销售者信息
2. **选择保障** - 查询并选择商品保障配置  
3. **准备订单** - 预处理订单信息
4. **提交订单** - 最终提交完整订单

## 关键设计原则

### 认证机制
- 所有API函数通过参数接收认证信息（j_token为必需，j_cookie可选）
- 无全局配置依赖，支持多租户场景
- OrderServiceFactory提供统一的服务实例创建

### 异步处理
- 基于asyncio + aiohttp的异步架构
- HttpClient使用异步上下文管理器确保资源正确释放
- 支持连接池和超时控制

### 配置管理
- OrderConfig: 业务参数配置（dataclass）
- HttpConfig: HTTP请求配置，包含端点映射和headers管理
- 配置与业务逻辑完全分离

## 常用命令

### 运行系统
```bash
python main.py
```

### 开发环境要求
- Python 3.7+ (当前使用 Python 3.13.5)
- Windows环境 (支付宝DLL需要Windows)
- 依赖包: aiohttp, asyncio (标准库)

### API使用示例
```python
from api import create_order_api

# 统一API接口（推荐）
result = await create_order_api(
    product_id="1418749602447905717",
    commodity_price="100.00",
    j_token="your_token",
    j_cookie="your_cookie"  # 可选
)

# 返回结果包含处理时间统计
# result["processing_time"] - 处理耗时（秒）
# result["start_time"] - 开始时间
# result["end_time"] - 结束时间
```

## 开发指导

### 添加新API端点
1. 在 `config.py` 的 `HttpConfig.ENDPOINTS` 中添加端点配置
2. 在 `order_service.py` 中实现业务逻辑方法
3. 在 `api.py` 中添加对外API接口
4. 更新 `main.py` 中的示例代码

### 修改业务配置
- 订单相关参数在 `OrderConfig` 类中统一管理
- HTTP相关配置在 `HttpConfig` 类中管理
- 避免硬编码配置值

### 错误处理
- API层统一返回结构化响应，包含success/error字段
- 业务层抛出具体异常，由API层统一捕获处理
- 调试模式下提供详细的错误信息和时间戳

### 代码风格
- 使用Google风格中文docstring
- 单文件不超过500行
- 优先使用相对导入
- 异步函数统一使用async/await语法

### 重要注意事项
- `alipay.dll`文件为支付签名库，需要Windows环境和特定的Python位数匹配
- 所有API请求需要有效的j_token认证
- 调试模式下会输出详细的请求和响应信息

## 项目文档

- `PLANNING.md`: 项目架构和设计文档
- `TASK.md`: 开发任务记录和变更历史
- `docs/`: 附加文档目录