"""
螃蟹支付订单创建系统 - 主入口模块

轻量级入口文件，演示API接口的使用方法
所有API接口已迁移到api.py模块中
"""
import asyncio
import json
from api import create_order_api, get_sellid
from DrissionPage import ChromiumPage,ChromiumOptions

async def main():
    """示例用法"""
    print("=== 螃蟹支付订单创建系统 ===")
    print("使用参数化的API接口\n")
    
    # 认证信息 - 请替换为实际值
    TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJ1c2VyIiwibG9naW5JZCI6Ijg0NzU1MSIsInJuU3RyIjoiM2Njekd2bEZ5TVBVbjJDY3ZtZGVWN3dBZmt0Uk10YVMiLCJ1c2VyTmFtZSI6IueUqOaIt18xNjcxNjI0NTIxNTUifQ.zH8evw8RCqW1SVCvsBYIBQ-tQSx-7fGjk88ntNqHlaE"    # 请替换为实际的 token
    COOKIE = 'gdp_user_id=gioenc-888a73c9%2C94g2%2C5bb0%2C8de5%2C821dc750c9db; a6990010a0b40f79_gdp_cs1=gioenc-956440; a6990010a0b40f79_gdp_gio_id=gioenc-956440; __snaker__id=nucRVUivn0QIv8cU; token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJ1c2VyIiwibG9naW5JZCI6Ijg0NzU1MSIsInJuU3RyIjoiM2Njekd2bEZ5TVBVbjJDY3ZtZGVWN3dBZmt0Uk10YVMiLCJ1c2VyTmFtZSI6IueUqOaIt18xNjcxNjI0NTIxNTUifQ.zH8evw8RCqW1SVCvsBYIBQ-tQSx-7fGjk88ntNqHlaE; a6990010a0b40f79_gdp_user_key=; Hm_lvt_6d8ffc7e04e74b3866a454a4477796ce=**********,**********; HMACCOUNT=473D465A55D4EE6A; deviceId=999a62b8-85f3-4cc1-9ed4-930eb641b8ec; gameBrowsingHistory=%7B%22browsingList%22%3A%5B%7B%22gameImage%22%3A%22https%3A%2F%2Fpublic-image.pxb7.com%2Fpxb7-upload%2Fgame%2Fimages%2F10302.jpg%22%2C%22gameId%22%3A%2210302%22%2C%22gameName%22%3A%22%E9%B8%A3%E6%BD%AE%22%7D%5D%7D; gdxidpyhxdE=3713RkkZ6fl762Bi7uQme7vX4mVAT6m9qdVv%2Ffh4BTbTmqIc05jD%2BkhPl%5C9gzdiZ9AB9rV1GH9VTsNkSBK80lH9OiVZ4NO9KZbBHYDqtI9%2BiGwq1g3xNHRC%2FSM7dSe%2BLiemKqAdcUkspausyap70zVr0WpH5ebErVBSfJcWg38%2BO2Lj3%3A1754294513524; acw_tc=0a18ab4417542937757704766e34f48ac5cb9a4a4b7afd1a9e7efeb4aa642a; a6990010a0b40f79_gdp_session_id=c5a7af2b-5436-4f45-a25d-5b8aeb2b694b; a6990010a0b40f79_gdp_session_id_sent=c5a7af2b-5436-4f45-a25d-5b8aeb2b694b; Hm_lpvt_6d8ffc7e04e74b3866a454a4477796ce=**********; pageParams=%7B%22pageParams%22%3Anull%7D; a6990010a0b40f79_gdp_sequence_ids={%22globalKey%22:4116}; ssxmod_itna=YqAxuQKeqCqqB0DhEDgDfxiKDIl1TBxBP01DpxYK0CODLxnRDGdKRNO+=HeqaKQaz0M+YKkRxGNPexA5Dnzx7YDtoSPh55aD0KwHFfigDG3e=Dnqpd5GlQh=3yD4oCBUTL7oh9AaL7it+D0aDmKDUhGO5xiiTx0rD0eDPxDYDG4DoOYDnO4DjxDdWfOvz8oDbxi3W4iaDGeDerEwDYveDDvTe407fiEqkCgKDGvCRr0FO63jebLoMUEx0I5pD7v3DlcP75l6OXZK+4Lngp3bt40kAq0Oz8RAKULQHbSvE89zPYRrqlGYHAmPlG3lKqjP47I5jw6jpGlhCjpsQK4GQeFK6jNrDDARhCehVt4xB+Yb5MC5lmy5ur7jD1CgrmBF90PbKiQTt70tShxR+QoDq/HPf0xO0DUDGBHPeD; ssxmod_itna2=YqAxuQKeqCqqB0DhEDgDfxiKDIl1TBxBP01DpxYK0CODLxnRDGdKRNO+=HeqaKQaz0M+YKkexDfCKY/BRPDLCrxxA374GXH3GUP3Dk/+1GaAeUWxL+=iL7FlqqiMo1G8PrsD=L+xrOVw8klaHaOG7Hoydezh7K0KGQQFHCOGxfxPGeGp+azOs1Ppp7EydQjyIrZ+hH4gzkjhFC2Kq=APODzOQOUvUapLFV+80ltSUC1IFRx78XYvvRf5hiAQOB6vmLfyDazhF=8pkKVhae80ItuI4iXPh+LdaF1SAL=vBk/iUZ+aiG6se1rNvqdeA3khQw+=XBz8EWITH34hijduTHmgP+41Cg7ridv+=eiQkrCMuOmrGAMi6Em3iNudpIEmRgWsDdfq322Iur32+QbmTbO7v3znuCaW=4eplMaMMWqiEeZvnhDDk1FxfjrGHb7LdkuSfTgkPTHSx7L8jeqhAMwjmdVCd4u7CLiRWaHEmMGbQCa9Zhxkw/xX3l5quOe3KjSFHjDPUp1gEplW2BAtLde97KnbP9GnxuFI11+h3l178d88bXiLSmQsYFEzt+R+mgdfZFgrc278361G2j2MdK41qDmD8Dg7DQ0Bcw3aG=HDxFxg92fQygis=NiiFgqRoQrExso=WvMGgdgxrfk3axqkk5b8l2wZiog8s13GrsOAYixy23LqVv4KY4+0rWG+GR2DMiN7DguHD4QDxKheGYQhkGDD'  # 请替换为实际的 cookie 字符串
    
    #初始化浏览器设置
    options = ChromiumOptions()
    options.headless(False)
    options.set_argument('--no-sandbox')
    options.set_argument('--disable-dev-shm-usage')
    options.set_argument('--no-images')
    driver = ChromiumPage(addr_or_opts=options)


    pay_password = "123456"
    # 示例1: 使用统一API接口
    print("示例1: 使用统一API接口创建订单")
    result = await create_order_api(
        product_id="1662230819389948435",
        commodity_price="23000",
        j_token=TOKEN,
        j_cookie=COOKIE,
        driver=driver,
        pay_password=pay_password
    )
    
    print("\n订单创建结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # print("\n" + "="*50)
    
    # # 示例2: 使用分步API接口
    # print("示例2: 使用分步API接口")
    # try:
    #     sellid_result = await get_sellid(
    #         product_id="1418749602447905717",
    #         j_token=TOKEN,
    #         j_cookie=COOKIE
    #     )
    #     print(f"获取销售者信息成功，响应长度: {len(sellid_result)}")
    # except Exception as e:
    #     print(f"获取销售者信息失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())