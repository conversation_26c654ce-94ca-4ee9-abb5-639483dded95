import ctypes
import re
import os

# 指定 alipay.dll 的完整路径
DLL_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "alipay.dll")

# 检查 DLL 文件是否存在
if not os.path.exists(DLL_PATH):
    print(f"Error: alipay.dll not found at {DLL_PATH}")
    exit(1)

# 加载 DLL
try:
    alipay_dll = ctypes.WinDLL(DLL_PATH)
except OSError as e:
    print(f"Failed to load alipay.dll: {e}")
    print("Ensure you are using a 32-bit Python environment and all DLL dependencies are available.")
    exit(1)

# 定义 get_Alipay 函数的返回类型和参数类型
alipay_dll.get_Alipay.argtypes = [ctypes.c_char_p]  # 参数为字符串
alipay_dll.get_Alipay.restype = ctypes.c_char_p     # 返回值为字符串

def get_alipay(body: str) -> str:
    try:
        # 将 Python 字符串转换为字节字符串（使用 GBK 编码）
        body_bytes = body.encode('gbk')  # 修改为 'gbk'
        
        # 调用 DLL 的 get_Alipay 函数
        result = alipay_dll.get_Alipay(body_bytes)
        
        # 将返回的 C 字符串转换为 Python 字符串（使用 GBK 编码）
        ret = result.decode('gbk')  # 修改为 'gbk'
        
        # 打印返回结果（对应易语言的调试输出）
        print("DLL 返回:", ret)
        
        # 提取 h5url（对应易语言的文本_取出中间文本）
        # 修正正则表达式，匹配 js://wappay('URL') 格式
        pattern = r"js://wappay\('(.*?)'\)"
        match = re.search(pattern, ret)
        h5url = match.group(1) if match else ""
        
        # 打印 h5url（对应易语言的调试输出）
        print("h5url:", h5url)
        
        return h5url
    
    except UnicodeDecodeError as e:
        print(f"Decoding error: {e}. Try another encoding like 'gb2312' or 'cp936'.")
        return ""
    except Exception as e:
        print(f"Error during get_alipay execution: {e}")
        return ""

# 示例调用
if __name__ == "__main__":
    body = "your_input_body_here"  # 替换为实际的输入参数
    h5url = get_alipay(body)