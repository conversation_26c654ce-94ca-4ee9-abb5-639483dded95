"""
业务异常模块

定义项目中使用的统一业务异常类
"""


class OrderServiceError(Exception):
    """订单服务基础异常类"""
    
    def __init__(self, message: str, error_code: str = None):
        """初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误代码
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code

    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class AuthenticationError(OrderServiceError):
    """认证异常"""
    
    def __init__(self, message: str = "认证失败，请检查token和cookie"):
        super().__init__(message, "AUTH_ERROR")


class ParameterValidationError(OrderServiceError):
    """参数验证异常"""
    
    def __init__(self, message: str, parameter_name: str = None):
        super().__init__(message, "PARAM_ERROR")
        self.parameter_name = parameter_name


class APIResponseError(OrderServiceError):
    """API响应异常"""
    
    def __init__(self, message: str, response_data: str = None):
        super().__init__(message, "API_ERROR")
        self.response_data = response_data


class OrderCreationError(OrderServiceError):
    """订单创建异常"""
    
    def __init__(self, message: str, step: str = None):
        super().__init__(message, "ORDER_ERROR")
        self.step = step


class PaymentError(OrderServiceError):
    """支付异常"""
    
    def __init__(self, message: str, order_id: str = None):
        super().__init__(message, "PAYMENT_ERROR")
        self.order_id = order_id


class NetworkError(OrderServiceError):
    """网络请求异常"""
    
    def __init__(self, message: str, url: str = None):
        super().__init__(message, "NETWORK_ERROR")
        self.url = url