"""
优化后的订单创建API模块

主要优化：
1. 异步并行化处理
2. HTTP连接池复用
3. 支付URL生成优化
4. JSON解析性能优化
5. 详细的性能监控
"""
import asyncio
import time
from typing import Dict, Any, Optional
from order_service import OptimizedOrderServiceFactory
from logger import get_logger

# JSON性能优化
try:
    import orjson as json_lib
    
    def json_loads(data: str):
        return json_lib.loads(data)
    
    def json_dumps(data, **kwargs):
        return json_lib.dumps(data).decode('utf-8')
        
except ImportError:
    import json as json_lib
    
    def json_loads(data: str):
        return json_lib.loads(data)
    
    def json_dumps(data, **kwargs):
        return json_lib.dumps(data, **kwargs)


class OptimizedAPI:
    """优化后的订单创建API类
    
    提供高性能的订单创建接口
    """
    
    def __init__(self, j_token: str, j_cookie: str = "", proxy: Optional[str] = None):
        """初始化优化后的API
        
        Args:
            j_token: 认证token
            j_cookie: Cookie字符串
            proxy: 代理服务器地址
        """
        self.j_token = j_token
        self.j_cookie = j_cookie
        self.proxy = proxy
        self.logger = get_logger(f"{__name__}.OptimizedAPI")
        
        # 性能统计
        self.performance_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0.0,
            "min_response_time": float('inf'),
            "max_response_time": 0.0
        }
    
    def _update_performance_stats(self, response_time: float, success: bool):
        """更新性能统计信息"""
        self.performance_stats["total_requests"] += 1
        
        if success:
            self.performance_stats["successful_requests"] += 1
        else:
            self.performance_stats["failed_requests"] += 1
        
        # 更新响应时间统计
        self.performance_stats["min_response_time"] = min(
            self.performance_stats["min_response_time"], response_time
        )
        self.performance_stats["max_response_time"] = max(
            self.performance_stats["max_response_time"], response_time
        )
        
        # 计算平均响应时间
        total_time = (self.performance_stats["average_response_time"] * 
                     (self.performance_stats["total_requests"] - 1) + response_time)
        self.performance_stats["average_response_time"] = total_time / self.performance_stats["total_requests"]
    
    async def create_order_optimized(
        self, 
        product_id: str, 
        commodity_price: str = "6500",
        sell_id: Optional[str] = None,
        config_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """优化后的订单创建接口
        
        Args:
            product_id: 商品ID
            commodity_price: 商品价格，默认6500
            sell_id: 销售者ID（可选，自动获取）
            config_id: 保障配置ID（可选，自动获取）
            
        Returns:
            订单创建结果字典
        """
        start_time = time.time()
        start_time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time))
        
        self.logger.info(f"创建订单服务实例 - 商品ID: {product_id}")
        self.logger.info(f"开始创建订单 - 商品ID: {product_id}")
        self.logger.info(f"[{start_time_str}] 开始创建订单 - 商品ID: {product_id}, 价格: {commodity_price}")
        
        result = None
        success = False
        
        try:
            # 🚀 使用优化后的订单服务
            async with OptimizedOrderServiceFactory.create_service(
                self.j_token, self.j_cookie, self.proxy
            ) as service:
                
                # 执行优化后的订单创建流程
                result = await service.create_order_complete_optimized(
                    product_id, commodity_price, sell_id, config_id
                )
                
                success = result.get("success", False)
                
                if success:
                    self.logger.info("成功生成支付宝支付URL")
                    self.logger.info(f"生成支付宝URL: {result.get('alipay_url')}")
                else:
                    self.logger.error(f"订单创建失败: {result.get('error')}")
        
        except Exception as e:
            error_msg = f"订单创建异常: {str(e)}"
            self.logger.error(error_msg)
            
            result = {
                "product_id": product_id,
                "steps": {},
                "success": False,
                "error": error_msg,
                "alipay_url": None,
                "body": None,
                "order_id": None,
                "errMessage": error_msg
            }
        
        # 计算总耗时
        end_time = time.time()
        end_time_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(end_time))
        processing_time = end_time - start_time
        
        # 更新结果中的时间信息
        if result:
            result["processing_time"] = round(processing_time, 4)
            result["start_time"] = start_time_str
            result["end_time"] = end_time_str
        
        # 更新性能统计
        self._update_performance_stats(processing_time, success)
        
        # 记录完成信息
        status = "成功" if success else "失败"
        self.logger.info(f"订单创建流程完成，耗时: {processing_time:.4f}秒，状态: {status}")
        self.logger.info(f"[{end_time_str}] 订单创建流程完成，耗时: {processing_time:.4f}秒")
        
        if success:
            self.logger.info("✅ 订单创建成功")
        else:
            self.logger.error("❌ 订单创建失败")
        
        # 输出结果
        print("\n订单创建结果:")
        print(json_dumps(result, indent=2))
        
        return result
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = self.performance_stats.copy()
        
        # 计算成功率
        if stats["total_requests"] > 0:
            stats["success_rate"] = (stats["successful_requests"] / stats["total_requests"]) * 100
        else:
            stats["success_rate"] = 0.0
        
        # 格式化时间
        if stats["min_response_time"] == float('inf'):
            stats["min_response_time"] = 0.0
        
        return stats
    
    def print_performance_report(self):
        """打印性能报告"""
        stats = self.get_performance_stats()
        
        print("\n" + "="*50)
        print("📊 性能统计报告")
        print("="*50)
        print(f"总请求数: {stats['total_requests']}")
        print(f"成功请求数: {stats['successful_requests']}")
        print(f"失败请求数: {stats['failed_requests']}")
        print(f"成功率: {stats['success_rate']:.2f}%")
        print(f"平均响应时间: {stats['average_response_time']:.4f}秒")
        print(f"最快响应时间: {stats['min_response_time']:.4f}秒")
        print(f"最慢响应时间: {stats['max_response_time']:.4f}秒")
        print("="*50)


# 便捷函数
async def create_order_optimized(
    product_id: str,
    j_token: str,
    commodity_price: str = "6500",
    j_cookie: str = "",
    proxy: Optional[str] = None,
    sell_id: Optional[str] = None,
    config_id: Optional[str] = None
) -> Dict[str, Any]:
    """便捷的优化订单创建函数
    
    Args:
        product_id: 商品ID
        j_token: 认证token
        commodity_price: 商品价格，默认6500
        j_cookie: Cookie字符串
        proxy: 代理服务器地址
        sell_id: 销售者ID（可选）
        config_id: 保障配置ID（可选）
        
    Returns:
        订单创建结果字典
    """
    api = OptimizedAPI(j_token, j_cookie, proxy)
    return await api.create_order_optimized(
        product_id, commodity_price, sell_id, config_id
    )


# 性能对比测试函数
async def performance_comparison_test(
    product_id: str,
    j_token: str,
    commodity_price: str = "6500",
    j_cookie: str = "",
    proxy: Optional[str] = None,
    test_count: int = 5
):
    """性能对比测试
    
    Args:
        product_id: 商品ID
        j_token: 认证token
        commodity_price: 商品价格
        j_cookie: Cookie字符串
        proxy: 代理服务器地址
        test_count: 测试次数
    """
    print(f"\n🚀 开始性能对比测试 - 测试次数: {test_count}")
    print("="*60)
    
    # 测试优化版本
    optimized_api = OptimizedAPI(j_token, j_cookie, proxy)
    
    print("测试优化版本...")
    optimized_times = []
    
    for i in range(test_count):
        print(f"优化版本测试 {i+1}/{test_count}")
        start_time = time.time()
        
        result = await optimized_api.create_order_optimized(
            product_id, commodity_price
        )
        
        end_time = time.time()
        test_time = end_time - start_time
        optimized_times.append(test_time)
        
        if result.get("success"):
            print(f"  ✅ 成功 - 耗时: {test_time:.4f}秒")
        else:
            print(f"  ❌ 失败 - 耗时: {test_time:.4f}秒")
        
        # 测试间隔
        await asyncio.sleep(1)
    
    # 计算统计信息
    avg_optimized = sum(optimized_times) / len(optimized_times)
    min_optimized = min(optimized_times)
    max_optimized = max(optimized_times)
    
    print("\n📊 性能测试结果:")
    print("="*60)
    print(f"优化版本平均耗时: {avg_optimized:.4f}秒")
    print(f"优化版本最快耗时: {min_optimized:.4f}秒")
    print(f"优化版本最慢耗时: {max_optimized:.4f}秒")
    print("="*60)
    
    # 打印详细性能报告
    optimized_api.print_performance_report()


if __name__ == "__main__":
    # 示例用法
    async def main():
        # 配置参数
        PRODUCT_ID = "1662225675305193250"
        J_TOKEN = "your_token_here"
        
        # 创建优化后的API实例
        api = OptimizedAPI(J_TOKEN)
        
        # 执行订单创建
        result = await api.create_order_optimized(PRODUCT_ID)
        
        # 打印性能报告
        api.print_performance_report()
    
    # 运行示例
    # asyncio.run(main())
