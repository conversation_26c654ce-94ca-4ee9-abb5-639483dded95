"""
HTTP客户端模块

统一管理HTTP请求，提供session复用和统一的错误处理
"""
import aiohttp
import asyncio
import json
from typing import Optional, Dict, Any, Union
from config import HttpConfig


class HttpClient:
    """统一HTTP客户端类
    
    提供GET和POST请求的统一接口，管理session生命周期
    """
    
    def __init__(self, config: HttpConfig, proxy: Optional[str] = None):
        """初始化HTTP客户端
        
        Args:
            config: HTTP配置对象
            proxy: 代理服务器地址
        """
        self.config = config
        self.proxy = proxy
        self._session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def _ensure_session(self):
        """确保session已创建"""
        if self._session is None or self._session.closed:
            connector = aiohttp.TCPConnector(limit=100, limit_per_host=10)
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            
            self._session = aiohttp.ClientSession(
                cookies=self.config.cookies,
                connector=connector,
                timeout=timeout
            )
    
    async def close(self):
        """关闭HTTP session"""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None
    
    async def get(self, endpoint: str, product_id: str, **url_params) -> str:
        """执行GET请求
        
        Args:
            endpoint: API端点名称
            product_id: 商品ID
            **url_params: URL参数
            
        Returns:
            响应文本内容
            
        Raises:
            aiohttp.ClientError: HTTP请求错误
        """
        await self._ensure_session()
        
        url = self.config.get_url(endpoint, **url_params)
        headers = self.config.get_get_headers(product_id)
        
        try:
            async with self._session.get(
                url, 
                headers=headers, 
                proxy=self.proxy
            ) as response:
                response.raise_for_status()
                return await response.text()
                
        except aiohttp.ClientError as e:
            raise aiohttp.ClientError(f"GET请求失败 {url}: {str(e)}") from e
    
    async def post(self, endpoint: str, product_id: str, data: Dict[str, Any]) -> str:
        """执行POST请求
        
        Args:
            endpoint: API端点名称
            product_id: 商品ID
            data: 请求体数据
            
        Returns:
            响应文本内容
            
        Raises:
            aiohttp.ClientError: HTTP请求错误
        """
        await self._ensure_session()
        
        url = self.config.get_url(endpoint)
        json_data = json.dumps(data)
        headers = self.config.get_post_headers(product_id, len(json_data))
        
        try:
            async with self._session.post(
                url,
                data=json_data,
                headers=headers,
                proxy=self.proxy
            ) as response:
                response.raise_for_status()
                return await response.text()
                
        except aiohttp.ClientError as e:
            raise aiohttp.ClientError(f"POST请求失败 {url}: {str(e)}") from e
    
    async def post_json_response(self, endpoint: str, product_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """执行POST请求并返回JSON响应
        
        Args:
            endpoint: API端点名称
            product_id: 商品ID
            data: 请求体数据
            
        Returns:
            解析后的JSON响应
            
        Raises:
            aiohttp.ClientError: HTTP请求错误
            json.JSONDecodeError: JSON解析错误
        """
        response_text = await self.post(endpoint, product_id, data)
        try:
            return json.loads(response_text)
        except json.JSONDecodeError as e:
            raise json.JSONDecodeError(f"响应JSON解析失败: {response_text}", e.doc, e.pos) from e


class HttpClientManager:
    """HTTP客户端管理器
    
    管理HTTP客户端的生命周期，提供便捷的创建方法
    """
    
    @staticmethod
    async def create_client(j_token: str, j_cookie: str = "", proxy: Optional[str] = None) -> HttpClient:
        """创建HTTP客户端实例
        
        Args:
            j_token: 认证token
            j_cookie: Cookie字符串
            proxy: 代理服务器地址
            
        Returns:
            配置好的HTTP客户端实例
        """
        config = HttpConfig(j_token, j_cookie)
        return HttpClient(config, proxy)
    
    @staticmethod
    def with_client(j_token: str, j_cookie: str = "", proxy: Optional[str] = None):
        """获取HTTP客户端的异步上下文管理器

        Args:
            j_token: 认证token
            j_cookie: Cookie字符串
            proxy: 代理服务器地址

        Returns:
            HTTP客户端异步上下文管理器

        Example:
            async with HttpClientManager.with_client(token, cookie) as client:
                response = await client.get("product_detail", product_id)
        """
        config = HttpConfig(j_token, j_cookie)
        return HttpClient(config, proxy)

    @staticmethod
    def create_persistent_session(j_token: str, j_cookie: str = "", proxy: Optional[str] = None) -> aiohttp.ClientSession:
        """创建持久化的HTTP会话 - 用于连接池复用优化

        Args:
            j_token: 认证token
            j_cookie: Cookie字符串
            proxy: 代理服务器地址

        Returns:
            配置好的aiohttp.ClientSession实例
        """
        config = HttpConfig(j_token, j_cookie)

        # 🚀 优化连接器配置
        connector = aiohttp.TCPConnector(
            limit=100,  # 总连接池大小
            limit_per_host=30,  # 每个主机的连接数
            keepalive_timeout=30,  # 保持连接时间
            enable_cleanup_closed=True,  # 启用清理已关闭的连接
            use_dns_cache=True,  # 启用DNS缓存
            ttl_dns_cache=300,  # DNS缓存TTL
            limit_simultaneous_connections=50  # 同时连接数限制
        )

        timeout = aiohttp.ClientTimeout(
            total=30,  # 总超时时间
            connect=10,  # 连接超时时间
            sock_read=20  # 读取超时时间
        )

        return aiohttp.ClientSession(
            cookies=config.cookies,
            connector=connector,
            timeout=timeout,
            headers=config.headers
        )

    @staticmethod
    def create_client_with_session(
        session: aiohttp.ClientSession,
        j_token: str,
        j_cookie: str = "",
        proxy: Optional[str] = None
    ) -> 'OptimizedHttpClient':
        """使用现有会话创建HTTP客户端 - 用于连接复用

        Args:
            session: 现有的aiohttp.ClientSession
            j_token: 认证token
            j_cookie: Cookie字符串
            proxy: 代理服务器地址

        Returns:
            使用现有会话的HTTP客户端
        """
        config = HttpConfig(j_token, j_cookie)
        return OptimizedHttpClient(session, config, proxy)


class OptimizedHttpClient:
    """优化后的HTTP客户端 - 支持会话复用"""

    def __init__(self, session: aiohttp.ClientSession, config: HttpConfig, proxy: Optional[str] = None):
        """初始化优化后的HTTP客户端

        Args:
            session: 现有的aiohttp.ClientSession
            config: HTTP配置对象
            proxy: 代理服务器地址
        """
        self.session = session
        self.config = config
        self.proxy = proxy

    async def get(self, endpoint: str, product_id: str, **params) -> str:
        """发送GET请求 - 复用会话版本

        Args:
            endpoint: API端点名称
            product_id: 商品ID（用于URL构建）
            **params: 查询参数

        Returns:
            响应文本

        Raises:
            aiohttp.ClientError: HTTP请求错误
        """
        url = self.config.build_url(endpoint, product_id)

        async with self.session.get(
            url,
            params=params,
            proxy=self.proxy
        ) as response:
            response.raise_for_status()
            return await response.text()

    async def post(self, endpoint: str, product_id: str, data: Dict[str, Any]) -> str:
        """发送POST请求 - 复用会话版本

        Args:
            endpoint: API端点名称
            product_id: 商品ID（用于URL构建）
            data: 请求数据

        Returns:
            响应文本

        Raises:
            aiohttp.ClientError: HTTP请求错误
        """
        url = self.config.build_url(endpoint, product_id)

        async with self.session.post(
            url,
            json=data,
            proxy=self.proxy
        ) as response:
            response.raise_for_status()
            return await response.text()